# Remove Objects Button 状态调试

## 问题分析

用户反馈：修改之后每次涂抹了，按钮还是禁用

## 根本原因

原来的实现中，`hasMaskContent` 函数每次都返回相同的函数引用，React无法检测到状态变化，导致按钮状态不会重新渲染。

## 解决方案

### 1. 添加状态管理
```typescript
const [maskContentExists, setMaskContentExists] = useState(false)
```

### 2. 创建状态更新函数
```typescript
const updateMaskContentState = useCallback(() => {
  const hasMask = maskCanvasRef.current?.hasMaskContent() || false
  setMaskContentExists(hasMask)
}, [])
```

### 3. 在关键时机更新状态

#### 绘制停止时
```typescript
<MaskCanvas
  // ... 其他props
  onStopDrawing={updateMaskContentState}
/>
```

#### 清除操作后
```typescript
const handleClearAll = useCallback(() => {
  if (maskCanvasRef.current?.clearAll) {
    maskCanvasRef.current.clearAll()
  }
  if (onClearAll) {
    onClearAll()
  }
  // Update mask state after clearing
  setTimeout(updateMaskContentState, 0)
}, [onClearAll, updateMaskContentState])
```

#### 撤销/重做操作后
```typescript
const undo = useCallback(() => {
  if (maskCanvasRef.current?.undo) {
    maskCanvasRef.current.undo()
    setTimeout(updateMaskContentState, 0)
  }
}, [updateMaskContentState])

const redo = useCallback(() => {
  if (maskCanvasRef.current?.redo) {
    maskCanvasRef.current.redo()
    setTimeout(updateMaskContentState, 0)
  }
}, [updateMaskContentState])
```

#### 图片切换时
```typescript
useEffect(() => {
  updateMaskContentState()
}, [imageData.id, initialMaskState, updateMaskContentState])
```

### 4. 使用状态而非函数调用
```typescript
// 修改前
hasMaskContent={() => maskCanvasRef.current?.hasMaskContent() || false}

// 修改后
hasMaskContent={() => maskContentExists}
```

## 工作流程

1. **用户开始绘制** → MaskCanvas检测到绘制开始
2. **用户停止绘制** → `onStopDrawing` 回调触发 → `updateMaskContentState` 执行
3. **检查mask内容** → `maskCanvasRef.current?.hasMaskContent()` 返回true
4. **更新状态** → `setMaskContentExists(true)` 
5. **React重新渲染** → EditorToolbar重新渲染，按钮变为可用状态

## 关键改进点

### 1. 状态驱动的UI更新
- 使用React状态管理按钮状态
- 确保状态变化时组件重新渲染

### 2. 异步状态更新
- 使用`setTimeout(updateMaskContentState, 0)`确保DOM更新完成后再检查状态
- 避免在canvas操作过程中检查状态

### 3. 全面的状态同步
- 涵盖所有可能改变mask内容的操作
- 包括绘制、清除、撤销、重做、图片切换

## 测试验证

### 基本功能测试
1. **初始状态**: 加载图片后按钮应为禁用状态
2. **绘制测试**: 绘制任意stroke后按钮应立即变为可用
3. **清除测试**: 清除所有内容后按钮应重新禁用
4. **撤销测试**: 撤销到空状态时按钮应禁用
5. **重做测试**: 重做有内容的状态时按钮应可用

### 边界情况测试
1. **快速绘制**: 快速连续绘制多个stroke
2. **图片切换**: 在有mask的图片间切换
3. **部分清除**: 清除部分但不是全部mask内容

## 预期结果

修复后，用户应该能看到：
- ✅ 绘制mask后按钮立即变为蓝色可用状态
- ✅ 清除mask后按钮立即变为灰色禁用状态  
- ✅ 撤销/重做操作后按钮状态正确更新
- ✅ 切换图片时按钮状态对应每张图片的mask状态
- ✅ 鼠标悬停时显示正确的提示信息
